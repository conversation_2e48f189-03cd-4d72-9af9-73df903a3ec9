import type { Mystery, FirestoreMystery, LegacySuspect, LegacyCrimeExplanation } from '@/types/mystery';
import { db } from './firebase';
import { doc, getDoc } from 'firebase/firestore';

const MOCK_MYSTERIES: Mystery[] = [
  {
    id: 'mystery1',
    title: 'The Case of the Crimson Quill',
    story: `Lord <PERSON>, a renowned and eccentric author, was discovered lifeless in his perpetually locked study—a room sealed from within, its only key resting in his cold hand. Beside him, a single, starkly crimson-tipped quill pen hinted at a story unfinished, or perhaps, one deliberately concluded. The heavy oak door was bolted, the windows latched tight against the encroaching twilight. Ashworth Manor, a house already burdened with secrets, now held a fresh, chilling enigma. Was this a final, tragic chapter penned by the author himself, or had a more sinister plot unfolded within these silent, watchful walls?`,
    suspects: [
      { id: 's1', name: 'Lady <PERSON>', description: 'The elegant, yet distant, widow, known for her impeccable taste and mounting gambling debts.', imageHint: 'aristocratic woman' },
      { id: 's2', name: 'Mr. <PERSON>', description: 'The stoic, ever-present butler, fiercely loyal, yet rumored to have been recently dismissed.', imageHint: 'stern butler' },
      { id: 's3', name: 'Ms. <PERSON>', description: 'A sharp-witted rival author, whose career was overshadowed by <PERSON>worth, and who was seen arguing with him days prior.', imageHint: 'intellectual writer' },
      { id: 's4', name: 'Dr. <PERSON> <PERSON>', description: 'The anxious family physician, who had prescribed Lord <PERSON>worth strong medication for a mysterious ailment.', imageHint: 'nervous doctor' }
    ],
    killerId: 's3',
    explanation: {
      motive: 'Ms. <PERSON>, driven by professional jealousy and the recent discovery that Lord <PERSON>worth had plagiarized her early work, sought to silence him and reclaim her narrative. The public argument was a calculated move to appear as an obvious, yet dismissible, suspect.',
      method: 'During a seemingly conciliatory visit, Ms. Reed switched Lord Ashworth\'s usual ink with a custom-concocted poison that is absorbed through the skin. Knowing his habit of chewing on his quill, she ensured the crimson tip was laced with the toxin. After he succumbed, she used a duplicate key (copied during a previous visit) to lock the study from the outside, then returned the original key to his hand and re-bolted the window from the outside using a thin, hooked wire, making it appear as a locked-room mystery.',
      keyEvidence: 'Traces of the rare toxin found on the crimson quill and under Ms. Reed\'s fingernails. A hidden compartment in her writing desk containing notes on Ashworth\'s habits and the poison formula. The "duplicate" key was found cleverly disguised as a letter opener on her person.'
    },
  },
  {
    id: 'mystery2',
    title: 'The Silent Symphony of Starfall Gallery',
    story: `The famed "Orion's Tear" necklace vanished from its high-tech display case at the Starfall Gallery during a private evening gala. No alarms blared, no lasers tripped. The vault-like room was sealed, its state-of-the-art sensors apparently undisturbed. The only anomaly: a single, out-of-place opera glove lying near the empty pedestal. The city's elite are baffled, and the gallery's reputation hangs by a thread.`,
    suspects: [
      { id: 's5', name: 'Baron Von Klaus', description: 'A debonair art collector with a shadowy reputation and an insatiable desire for unique jewels.', imageHint: 'suave baron' },
      { id: 's6', name: 'Seraphina "Silas" Dubois', description: 'A phantom-like jewel thief, whispered to have returned from a decade of silence.', imageHint: 'mysterious woman' },
      { id: 's7', name: 'Mr. Alistair Finch (no relation)', description: 'The gallery\'s jittery head of security, whose meticulous routines were legendary, yet he was conspicuously absent during the critical timeframe.', imageHint: 'anxious guard' },
      { id: 's8', name: 'Eleonora Vancroft', description: 'The gallery’s enigmatic curator, an expert in ancient security mechanisms, who designed the display case herself.', imageHint: 'elegant curator' },
    ],
    killerId: 's8', // "Killer" in this context is the thief
    explanation: {
      motive: 'Eleonora Vancroft, facing financial ruin due to a series of disastrous art investments, orchestrated the theft to sell the necklace on the clandestine art market. She planned to use the funds to cover her losses and disappear.',
      method: 'Vancroft exploited a hidden acoustic override she built into the display case’s security system, known only to her. Using a specific sequence of ultrasonic frequencies emitted from a device concealed in her evening bag, she temporarily disabled all sensors and locks without triggering any alarms. The opera glove was a red herring, planted to mislead investigators towards a more flamboyant thief.',
      keyEvidence: 'Financial records detailing Vancroft\'s massive debts. Schematics of the display case found in her private safe, with annotations revealing the acoustic override. The ultrasonic emitter device, cleverly disguised as a luxury perfume atomizer, discovered in her possession.'
    },
  },
];

// Helper function to transform Firestore data to legacy format
const transformFirestoreMystery = (firestoreData: FirestoreMystery, docId: string): Mystery => {
  // Transform suspects to legacy format with generated IDs
  const legacySuspects: LegacySuspect[] = firestoreData.Suspects.map((suspect, index) => ({
    id: `suspect_${index + 1}`,
    name: suspect.name,
    description: `${suspect.motive} ${suspect.means} ${suspect.opportunity}`.trim(),
    imageHint: 'mysterious person' // Default hint for AI-generated images
  }));

  // Find the killer ID by matching the killer name from Resolution
  const killerName = firestoreData.Resolution.killer;
  const killerId = legacySuspects.find(s => s.name === killerName)?.id || 'suspect_1';

  // Transform explanation to legacy format
  const legacyExplanation: LegacyCrimeExplanation = {
    motive: firestoreData.Resolution.motive,
    method: firestoreData.Resolution.method,
    keyEvidence: firestoreData.Resolution.key_evidence
  };

  return {
    id: docId,
    title: firestoreData.Title,
    story: firestoreData.FullText,
    suspects: legacySuspects,
    killerId,
    explanation: legacyExplanation
  };
};

// Helper function to get today's date in yyyyMMdd format
const getTodayDateId = (): string => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// Fallback function using mock data
const getFallbackMystery = (): Mystery => {
  const today = new Date();
  const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
  const mysteryIndex = dayOfYear % MOCK_MYSTERIES.length;
  return MOCK_MYSTERIES[mysteryIndex];
};

export const getDailyMystery = async (): Promise<Mystery> => {
  try {
    // Get today's date in the expected format
    const todayId = getTodayDateId();

    // Reference to the mystery document
    const mysteryRef = doc(db, 'mysteries', todayId);

    // Fetch the document
    const mysterySnap = await getDoc(mysteryRef);

    if (mysterySnap.exists()) {
      const firestoreData = mysterySnap.data() as FirestoreMystery;
      return transformFirestoreMystery(firestoreData, todayId);
    } else {
      console.warn(`No mystery found for date ${todayId}, falling back to mock data`);
      return getFallbackMystery();
    }
  } catch (error) {
    console.error('Error fetching mystery from Firestore:', error);
    console.warn('Falling back to mock data');
    return getFallbackMystery();
  }
};
